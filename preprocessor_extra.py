#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

解耦CNN+MLP特徵處理架構說明:
1. 數據流解耦: Preprocessor輸出特徵字典，模型內部處理CNN
2. CNN特徵提取器: 在主模型內部處理5個11x11的地圖通道 (obstacle, end_point, treasure, buff, memory_map)
3. 端到端訓練: 梯度可以從輸出流回CNN，支持完整的端到端訓練
4. 動作遮罩: legal_action作為遮罩在模型中使用，不作為特徵輸入
5. 狀態感知: 添加閃現冷卻等關鍵狀態信息

特徵組成:
- 地圖特徵: 5×11×11 = 605維 (在模型中通過CNN處理為64維)
- 非地圖特徵: 2+6+6+6+3 = 23維
  - 位置特徵: 2 + 6 + 6 + 6 = 20維
  - 閃現狀態特徵: 3維 (可用性、冷卻時間、移動可用性)

架構優勢:
- 支持端到端CNN訓練，梯度流完整
- 模型內部動作遮罩，更高效的非法動作處理
- 關鍵狀態信息（如flash_cooldown）可被模型感知
- 數據流清晰，便於調試和優化
"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)



class Preprocessor:
    def __init__(self) -> None:
        self.move_action_num = 8
        self.flash_action_num = 8  # 閃現動作數量
        self.total_action_num = self.move_action_num + self.flash_action_num  # 總動作數量 16

        # 局部視野設置
        self.local_view_size = 11  # 11x11 局部視野
        self.local_view_half = self.local_view_size // 2  # 5

        # 移除CNN特徵提取器，將其移到模型內部
        # CNN現在在主模型中處理，以支持端到端訓練

        self.reset()
        self.prev_end_dist = 0.0  # 新增: 記錄上一步的end_dist
        # 初始化寶箱距離列表
        self.treasure_distance_list = []
        self.prev_treasure_distance_list = []
        # 初始化終點位置
        self.real_end_pos = None
        self.real_end_dist = 0.0
        self.prev_real_end_dist = 0.0



    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        
        # 終點相關
        self.end_pos = None
        self.is_end_pos_found = False
        self.end_status = -1
        
        # 歷史位置
        self.history_pos = []
        self.bad_move_ids = set()
        
        # 閃現相關狀態
        self.flash_cooldown = 0  # 閃現冷卻時間
        self.flash_usable = True  # 閃現是否可用
        
        # 官方局部視野數據
        self.map_info = None
        
        # 記憶地圖：記錄智能體探索每一個網格區域的次數
        self.memory_map = np.zeros((self.local_view_size, self.local_view_size), dtype=np.float32)

        self.treasure_pos = None
        self.is_treasure_pos_found = False
        self.treasure_status = -1
        self.prev_end_dist = 0.0  # 新增: reset時也要重置
        # 初始化寶箱距離列表
        self.treasure_distance_list = []
        self.prev_treasure_distance_list = []
        # 初始化終點位置
        self.real_end_pos = None
        self.real_end_dist = 0.0
        self.prev_real_end_dist = 0.0

    def _get_pos_feature(self, found, cur_pos, target_pos):
        if target_pos is None:
            target_pos = cur_pos
            
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def _process_map_info(self, obs):
        """處理官方提供的局部視野信息，提取不同類型的地圖信息"""
        # 獲取11x11的局部視野數據
        map_data = []
        for row in obs["map_info"]:
            map_data.append(row["values"])
        
        # 轉換為numpy數組 (11, 11)
        self.map_info = np.array(map_data, dtype=np.float32)
        
        # 從局部視野中提取不同類型的信息
        # 0表示不可通行，1表示可以通行，2表示起點位置，3表示终点位置，4表示宝箱位置，6表示加速增益位置
        
        # 障礙物 (0表示不可通行)
        obstacle = (self.map_info == 0).astype(np.float32)
        
        # 終點位置 (3)  
        end_point = (self.map_info == 3).astype(np.float32)
        
        # 寶箱位置 (4)
        treasure = (self.map_info == 4).astype(np.float32)
        
        # 加速增益位置 (6)
        buff = (self.map_info == 6).astype(np.float32)
        
        return {
            'obstacle': obstacle,
            'end_point': end_point,
            'treasure': treasure,
            'buff': buff
        }

    def _prepare_map_features(self, map_features, memory_map):
        """準備地圖特徵，返回5通道的地圖數據"""
        # 將5個11x11的地圖堆疊成5通道的輸入
        # 順序: obstacle, end_point, treasure, buff, memory_map
        map_stack = np.stack([
            map_features['obstacle'],
            map_features['end_point'],
            map_features['treasure'],
            map_features['buff'],
            memory_map
        ], axis=0)  # Shape: (5, 11, 11)

        return map_stack

        

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]
        # 先保存上一個step的寶箱距離列表
        self.prev_treasure_distance_list = self.treasure_distance_list.copy() if hasattr(self, 'treasure_distance_list') else []
        treasure_distance_list = []
        if _ is not None:
            # 保存上一個step的 real_end_dist
            self.prev_real_end_dist = self.real_end_dist
            # 解析當前end_pos
            if 'game_info' in _ and 'end_pos' in _['game_info']:
                end_pos_dict = _['game_info']['end_pos']
                self.real_end_pos = (end_pos_dict.get('x', 0), end_pos_dict.get('z', 0))
            # 計算 real_end_dist
            if self.real_end_pos is not None:
                agent_x = _['game_info']['pos']['x']
                agent_z = _['game_info']['pos']['z']
                self.real_end_dist = math.sqrt((agent_x - self.real_end_pos[0]) ** 2 + (agent_z - self.real_end_pos[1]) ** 2)
            else:
                self.real_end_dist = 0.0
            # 補回寶箱距離計算邏輯
            agent_x = _['game_info']['pos']['x']
            agent_z = _['game_info']['pos']['z']
            for organ in _['frame_state']['organs']:
                if organ.get('sub_type') == 1 and organ.get('status') != 0:
                    treasure_x = organ['pos']['x']
                    treasure_z = organ['pos']['z']
                    distance = math.sqrt((treasure_x - agent_x) ** 2 + (treasure_z - agent_z) ** 2)
                    treasure_distance_list.append(distance)
        self.treasure_distance_list = treasure_distance_list
        # 處理官方提供的局部視野信息
        self.map_features = self._process_map_info(obs)
        
        hero = obs["frame_state"]["heroes"][0]
        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 更新記憶地圖：當前位置對應的網格區域值+0.2，最大為1
        # 局部視野中心點對應當前位置
        center_x, center_y = self.local_view_half, self.local_view_half
        self.memory_map[center_x, center_y] = min(1.0, self.memory_map[center_x, center_y] + 0.2)

        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

        # 處理所有物件信息
        organs = obs["frame_state"]["organs"]
        
        # 處理終點物件
        for organ in organs:
            if organ["sub_type"] == 4:  # 終點
                end_pos_dis = RelativeDistance[organ["relative_pos"]["l2_distance"]]
                end_pos_dir = RelativeDirection[organ["relative_pos"]["direction"]]
                self.end_status = organ["status"]

                if organ["status"] != -1:
                    self.end_pos = (organ["pos"]["x"], organ["pos"]["z"])
                    self.is_end_pos_found = True
                # if end_pos is not found, use relative position to predict end_pos
                # 如果终点位置未找到，使用相对位置预测终点位置
                elif (not self.is_end_pos_found) and (
                    self.end_pos is None
                    or self.step_no % 100 == 0
                    or getattr(self, 'end_pos_dir', None) != end_pos_dir
                    or getattr(self, 'end_pos_dis', None) != end_pos_dis
                ):
                    distance = end_pos_dis * 20
                    theta = DirectionAngles[end_pos_dir]
                    delta_x = distance * math.cos(math.radians(theta))
                    delta_z = distance * math.sin(math.radians(theta))

                    self.end_pos = (
                        max(0, min(128, round(self.cur_pos[0] + delta_x))),
                        max(0, min(128, round(self.cur_pos[1] + delta_z))),
                    )

                    self.end_pos_dir = end_pos_dir
                    self.end_pos_dis = end_pos_dis

        # 更新位置相關特徵
        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)
        
        # 初始化位置信息（終點方向信息）
        self.init_pos_feature = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)[:6]
        
        # 目標位置特徵（主要是終點）
        self.feature_end_pos = self._get_pos_feature(self.is_end_pos_found, self.cur_pos, self.end_pos)

        # 歷史位置特徵
        if len(self.history_pos) > 1:
            self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])
        else:
            self.feature_history_pos = self._get_pos_feature(0, self.cur_pos, self.cur_pos)

        self.move_usable = True
        
        # 更新閃現冷卻狀態
        if self.flash_cooldown > 0:
            self.flash_cooldown -= 1
            self.flash_usable = False
        else:
            self.flash_usable = True
            
        # 如果上一個動作是閃現動作（8-15），啟動冷卻
        if last_action >= 8 and last_action <= 15:
            self.flash_cooldown = 100  # 根據環境配置設置冷卻時間
            self.flash_usable = False
            
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)
        
        # Legal action
        # 合法动作
        legal_action = self.get_legal_action()

        # 準備地圖特徵（5通道地圖數據）
        map_features_array = self._prepare_map_features(self.map_features, self.memory_map)

        # 添加閃現狀態特徵
        flash_state_features = self._get_flash_state_features()

        # 非地圖特徵 - 移除legal_action作為特徵，改為遮罩使用
        non_map_features = np.concatenate([
            # 2 归一化的位置信息
            self.cur_pos_norm,

            # 6 初始化位置信息（终点方向信息）
            self.init_pos_feature,

            # 6 目标位置特征（终点）
            self.feature_end_pos,

            # 6 历史位置特征
            self.feature_history_pos,

            # 3 閃現狀態特徵（替代原來的16維legal_action）
            flash_state_features,
        ])

        # 返回特徵字典而不是扁平向量
        feature_dict = {
            'map_features': map_features_array,      # Shape: (5, 11, 11)
            'non_map_features': non_map_features     # Shape: (23,)
        }

        # 計算當前位置的記憶地圖值（用於重複訪問懲罰）
        center_x, center_y = self.local_view_half, self.local_view_half
        current_memory_value = self.memory_map[center_x, center_y]

        prev_end_dist = self.prev_end_dist
        cur_end_dist = self.feature_end_pos[-1]
        reward = reward_process(
            prev_end_dist, cur_end_dist,
            self.feature_history_pos[-1],
            current_memory_value,
            self.treasure_distance_list, self.prev_treasure_distance_list,
            self.prev_real_end_dist, self.real_end_dist,
            frame_state[0]['frame_state']['step_no']
        )
        self.prev_end_dist = cur_end_dist  # 更新prev_end_dist

        return (
            feature_dict,
            legal_action,
            reward,
        )

    def get_legal_action(self):
        # if last_action is move and current position is the same as last position, add this action to bad_move_ids
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中
        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
            and self.last_action < 8  # 只對移動動作檢查
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            # 如果位置有變化，清空bad_move_ids
            if (abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) >= 0.001 or 
                abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) >= 0.001):
                self.bad_move_ids = set()

        # 初始化16個動作的合法性
        legal_action = []
        
        # 前8個動作：移動動作 (0-7)
        move_actions = [self.move_usable] * self.move_action_num
        for move_id in self.bad_move_ids:
            if move_id < self.move_action_num:
                move_actions[move_id] = 0
        legal_action.extend(move_actions)
        
        # 後8個動作：閃现動作 (8-15)
        flash_actions = [self.flash_usable] * self.flash_action_num
        legal_action.extend(flash_actions)

        # 確保至少有一個動作可用
        if not any(legal_action[:self.move_action_num]) and not any(legal_action[self.move_action_num:]):
            # 如果所有動作都不可用，重置並允許所有移動動作
            self.bad_move_ids = set()
            legal_action = [1] * self.total_action_num

        return legal_action

    def _get_flash_state_features(self):
        """獲取閃現狀態特徵，替代將legal_action作為特徵輸入"""
        # 3維閃現狀態特徵：
        # 1. 閃現是否可用 (0/1)
        # 2. 閃現冷卻時間歸一化 (0-1)
        # 3. 移動動作可用性比例 (0-1)

        flash_available = float(self.flash_usable)
        flash_cooldown_norm = norm(self.flash_cooldown, 100, 0)  # 假設最大冷卻時間為100

        # 計算可用移動動作的比例
        move_actions_available = sum(1 for i in range(self.move_action_num)
                                   if i not in self.bad_move_ids and self.move_usable)
        move_availability_ratio = move_actions_available / self.move_action_num

        return np.array([flash_available, flash_cooldown_norm, move_availability_ratio], dtype=np.float32)


